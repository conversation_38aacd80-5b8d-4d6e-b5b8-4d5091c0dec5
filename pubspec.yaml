name: code_qr_app
description: Application Flutter moderne pour générer des codes QR
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  
  # UI & Material Design
  cupertino_icons: ^1.0.6
  material_color_utilities: ^0.11.1
  
  # QR Code generation
  qr_flutter: ^4.1.0
  
  # Storage & Data persistence
  shared_preferences: ^2.2.2
  
  # File operations
  path_provider: ^2.1.1
  
  # Sharing & Gallery save
  share_plus: ^7.2.1
  gallery_saver: ^2.3.2
  
  # Permissions
  permission_handler: ^11.0.1
  
  # State management
  provider: ^6.1.1
  
  # Image processing
  image: ^4.1.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true
  generate: true
  
  # assets:
  #   - assets/images/
  
  # fonts:
  #   - family: Roboto
  #     fonts:
  #       - asset: fonts/Roboto-Regular.ttf
  #       - asset: fonts/Roboto-Medium.ttf
  #         weight: 500
  #       - asset: fonts/Roboto-Bold.ttf
  #         weight: 700
