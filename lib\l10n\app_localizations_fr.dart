// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for French (`fr`).
class AppLocalizationsFr extends AppLocalizations {
  AppLocalizationsFr([String locale = 'fr']) : super(locale);

  @override
  String get appTitle => 'Générateur de Code QR';

  @override
  String get generateQRCode => 'Générer le Code QR';

  @override
  String get enterText => 'Entrez du texte ou une URL';

  @override
  String get enterTextToGenerate => 'Entrez du texte pour générer un code QR';

  @override
  String get shareQRCode => 'Partager le Code QR';

  @override
  String get saveToGallery => 'Enregistrer dans la Galerie';

  @override
  String get history => 'Historique';

  @override
  String get generator => 'Générateur';

  @override
  String get qrCodeSaved => 'Code QR enregistré dans la galerie';

  @override
  String get errorSaving => 'Erreur lors de l\'enregistrement';

  @override
  String get noHistoryYet => 'Aucun code QR généré pour le moment';

  @override
  String get clearHistory => 'Vider l\'Historique';

  @override
  String get confirmClearHistory =>
      'Êtes-vous sûr de vouloir effacer tout l\'historique ?';

  @override
  String get cancel => 'Annuler';

  @override
  String get confirm => 'Confirmer';

  @override
  String get createdAt => 'Créé le';
}
