# QR Code Generator App 📱

Une application Flutter moderne et élégante pour générer, sauvegarder et partager des codes QR.

## ✨ Fonctionnalités

- **Génération de codes QR** : Créez des codes QR à partir de texte ou d'URLs
- **Interface moderne** : Design Material 3 avec animations fluides
- **Thèmes automatiques** : Mode clair/sombre qui suit les préférences système
- **Localisation** : Support français et anglais avec détection automatique
- **Historique** : Sauvegarde automatique des codes QR générés
- **Partage et sauvegarde** : Partagez ou enregistrez vos codes QR dans la galerie
- **Interface responsive** : S'adapte à toutes les tailles d'écran

## 📱 Captures d'écran

L'application dispose de deux écrans principaux :
- **Générateur** : Interface pour saisir du texte et générer des codes QR
- **Historique** : Liste de tous les codes QR précédemment générés

## 🚀 Installation

### Prérequis
- Flutter SDK (>=3.10.0)
- Dart SDK (>=3.0.0)

### Étapes d'installation

1. Clonez le repository :
```bash
git clone <repository-url>
cd code_qr_app
```

2. Installez les dépendances :
```bash
flutter pub get
```

3. Générez les fichiers de localisation :
```bash
flutter gen-l10n
```

4. Lancez l'application :
```bash
flutter run
```

## 📦 Dépendances principales

- `qr_flutter`: Génération de codes QR
- `provider`: Gestion d'état
- `shared_preferences`: Stockage local
- `gallery_saver`: Sauvegarde dans la galerie
- `share_plus`: Partage de fichiers
- `path_provider`: Accès aux répertoires système

## 🏗️ Architecture

L'application suit une architecture MVVM avec Provider :

```
lib/
├── l10n/                 # Fichiers de localisation
├── models/               # Modèles de données
├── providers/            # Providers (état global)
├── screens/              # Écrans de l'application
└── main.dart            # Point d'entrée
```

### Providers
- **ThemeProvider** : Gestion des thèmes clair/sombre
- **QRProvider** : Gestion des codes QR et de l'historique

## 🎨 Design

L'application utilise :
- **Material Design 3** avec couleurs dynamiques
- **Animations fluides** pour les transitions
- **Coins arrondis** et **ombres modernes**
- **Icônes Material** et police Roboto
- **Interface adaptative** pour mobile

## 🌍 Localisation

Support intégré pour :
- 🇫🇷 Français
- 🇬🇧 Anglais

La langue est détectée automatiquement selon les paramètres système.

## 📱 Permissions

L'application nécessite les permissions suivantes :
- **Stockage** : Pour sauvegarder les codes QR
- **Partage** : Pour partager les codes QR

## 🔧 Configuration

### Android
Ajoutez dans `android/app/src/main/AndroidManifest.xml` :
```xml
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
```

### iOS
Ajoutez dans `ios/Runner/Info.plist` :
```xml
<key>NSPhotoLibraryAddUsageDescription</key>
<string>Cette app a besoin d'accéder à la galerie pour sauvegarder les codes QR</string>
```

## 🛠️ Développement

### Ajouter une nouvelle langue

1. Créez un nouveau fichier ARB dans `lib/l10n/` (ex: `app_es.arb`)
2. Ajoutez la locale dans `supportedLocales` dans `main.dart`
3. Régénérez les localisations : `flutter gen-l10n`

### Personnaliser les thèmes

Les thèmes sont définis dans `main.dart` avec les couleurs Material 3. Modifiez les `ColorScheme` pour personnaliser l'apparence.

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 🤝 Contribution

Les contributions sont les bienvenues ! N'hésitez pas à :
- Ouvrir des issues pour signaler des bugs
- Proposer des améliorations
- Soumettre des pull requests

---

Développé avec ❤️ en Flutter
