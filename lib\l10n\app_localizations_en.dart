// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'QR Code Generator';

  @override
  String get generateQRCode => 'Generate QR Code';

  @override
  String get enterText => 'Enter text or URL';

  @override
  String get enterTextToGenerate => 'Enter some text to generate a QR code';

  @override
  String get shareQRCode => 'Share QR Code';

  @override
  String get saveToGallery => 'Save to Gallery';

  @override
  String get history => 'History';

  @override
  String get generator => 'Generator';

  @override
  String get qrCodeSaved => 'QR Code saved to gallery';

  @override
  String get errorSaving => 'Error saving QR code';

  @override
  String get noHistoryYet => 'No QR codes generated yet';

  @override
  String get clearHistory => 'Clear History';

  @override
  String get confirmClearHistory =>
      'Are you sure you want to clear all history?';

  @override
  String get cancel => 'Cancel';

  @override
  String get confirm => 'Confirm';

  @override
  String get createdAt => 'Created at';
}
