import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:path_provider/path_provider.dart';
import 'package:gallery_saver/gallery_saver.dart';
import 'package:share_plus/share_plus.dart';
import '../models/qr_item.dart';

class QRProvider extends ChangeNotifier {
  static const String _historyKey = 'qr_history';
  
  List<QRItem> _history = [];
  String _currentText = '';
  bool _isLoading = false;

  List<QRItem> get history => List.unmodifiable(_history);
  String get currentText => _currentText;
  bool get isLoading => _isLoading;

  QRProvider() {
    _loadHistory();
  }

  void _loadHistory() async {
    final prefs = await SharedPreferences.getInstance();
    final historyJson = prefs.getStringList(_historyKey) ?? [];
    
    _history = historyJson
        .map((item) => QRItem.fromJson(jsonDecode(item)))
        .toList();
    _history.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    notifyListeners();
  }

  void _saveHistory() async {
    final prefs = await SharedPreferences.getInstance();
    final historyJson = _history
        .map((item) => jsonEncode(item.toJson()))
        .toList();
    await prefs.setStringList(_historyKey, historyJson);
  }

  void updateCurrentText(String text) {
    _currentText = text;
    notifyListeners();
  }

  void addToHistory(String text) {
    if (text.trim().isEmpty) return;
    
    // Check if this text already exists in recent history
    final existingIndex = _history.indexWhere((item) => item.text == text);
    if (existingIndex != -1) {
      // Remove existing and add to top
      _history.removeAt(existingIndex);
    }
    
    final newItem = QRItem(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      text: text,
      createdAt: DateTime.now(),
    );
    
    _history.insert(0, newItem);
    
    // Keep only the last 50 items
    if (_history.length > 50) {
      _history = _history.sublist(0, 50);
    }
    
    _saveHistory();
    notifyListeners();
  }

  void removeFromHistory(String id) {
    _history.removeWhere((item) => item.id == id);
    _saveHistory();
    notifyListeners();
  }

  void clearHistory() {
    _history.clear();
    _saveHistory();
    notifyListeners();
  }

  Future<bool> saveQRToGallery(GlobalKey qrKey) async {
    try {
      _isLoading = true;
      notifyListeners();
      
      final RenderRepaintBoundary boundary =
          qrKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
      final ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      final ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final Uint8List pngBytes = byteData!.buffer.asUint8List();

      final Directory tempDir = await getTemporaryDirectory();
      final String fileName = 'qr_code_${DateTime.now().millisecondsSinceEpoch}.png';
      final File file = File('${tempDir.path}/$fileName');
      await file.writeAsBytes(pngBytes);

      final bool? result = await GallerySaver.saveImage(file.path);
      
      _isLoading = false;
      notifyListeners();
      
      return result ?? false;
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  Future<void> shareQR(GlobalKey qrKey, String text) async {
    try {
      _isLoading = true;
      notifyListeners();
      
      final RenderRepaintBoundary boundary =
          qrKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
      final ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      final ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final Uint8List pngBytes = byteData!.buffer.asUint8List();

      final Directory tempDir = await getTemporaryDirectory();
      final String fileName = 'qr_code_${DateTime.now().millisecondsSinceEpoch}.png';
      final File file = File('${tempDir.path}/$fileName');
      await file.writeAsBytes(pngBytes);

      await Share.shareXFiles(
        [XFile(file.path)],
        text: 'QR Code: $text',
      );
      
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      notifyListeners();
    }
  }
}
